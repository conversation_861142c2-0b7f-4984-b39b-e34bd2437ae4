package handlers

import (
	"fmt"
	"go-expense/services"
	"html/template"
	"net/http"
	"strconv"
)

// AIHandler handles AI-powered expense input
type AI<PERSON>andler struct {
	aiService      services.AIServiceInterface
	expenseService services.ExpenseServiceInterface
	templates      *template.Template
}

// AIInputData represents data for the AI input page
type AIInputData struct {
	PageData
	Error string
}

// AIConfirmData represents data for the AI confirmation page
type AIConfirmData struct {
	PageData
	ParsedExpense *services.ParsedExpense
	Error         string
	Success       bool
}

// NewAIHandler creates a new AI handler
func NewAIHandler(aiService services.AIServiceInterface, expenseService services.ExpenseServiceInterface) *AIHandler {
	// Create template functions
	funcMap := template.FuncMap{
		"add": func(a, b float64) float64 {
			return a + b
		},
		"mul": func(a, b float64) float64 {
			return a * b
		},
		"formatCurrency": func(amount float64) string {
			return formatCurrency(amount)
		},
	}

	// Parse templates
	templates := template.Must(template.New("").Funcs(funcMap).ParseGlob("templates/*.html"))

	return &AIHandler{
		aiService:      aiService,
		expenseService: expenseService,
		templates:      templates,
	}
}

// AIInput shows the AI input form
func (h *AIHandler) AIInput(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := AIInputData{
		PageData: PageData{
			Title:      "AI Expense Input",
			ActivePage: "ai-input",
		},
	}

	if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}

// AIParse processes the AI input and shows confirmation
func (h *AIHandler) AIParse(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse form data
	if err := r.ParseForm(); err != nil {
		http.Error(w, "Failed to parse form", http.StatusBadRequest)
		return
	}

	inputText := r.FormValue("expense_text")
	if inputText == "" {
		h.renderAIInputWithError(w, "Please enter expense description")
		return
	}

	// Parse with AI
	parsed, err := h.aiService.ParseExpenseText(inputText)
	if err != nil {
		h.renderAIInputWithError(w, fmt.Sprintf("Failed to parse expense: %s", err.Error()))
		return
	}

	// Show confirmation page
	data := AIConfirmData{
		PageData: PageData{
			Title:      "Confirm AI Parsed Expense",
			ActivePage: "ai-input",
		},
		ParsedExpense: parsed,
	}

	if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}

// AIConfirm saves the confirmed expense
func (h *AIHandler) AIConfirm(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse form data
	if err := r.ParseForm(); err != nil {
		http.Error(w, "Failed to parse form", http.StatusBadRequest)
		return
	}

	// Get form values
	amountStr := r.FormValue("amount")
	category := r.FormValue("category")
	subcategory := r.FormValue("subcategory")
	description := r.FormValue("description")
	date := r.FormValue("date")
	additionalAmountStr := r.FormValue("additional_amount")

	// Validate and convert amount
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil || amount <= 0 {
		h.renderConfirmWithError(w, r, "Invalid amount")
		return
	}

	// Validate and convert additional amount
	additionalAmount := 0.0
	if additionalAmountStr != "" {
		additionalAmount, err = strconv.ParseFloat(additionalAmountStr, 64)
		if err != nil {
			additionalAmount = 0.0
		}
	}

	// Create expense
	expense := &services.ParsedExpense{
		Amount:           amount,
		Category:         category,
		Subcategory:      subcategory,
		Description:      description,
		Date:             date,
		AdditionalAmount: additionalAmount,
	}

	// Convert to models.Expense and save
	modelExpense := expense.ConvertToExpense()
	if err := h.expenseService.CreateExpense(modelExpense); err != nil {
		h.renderConfirmWithError(w, r, fmt.Sprintf("Failed to save expense: %s", err.Error()))
		return
	}

	// Redirect to expenses list
	http.Redirect(w, r, "/ui/expenses", http.StatusSeeOther)
}

// renderAIInputWithError renders the AI input page with an error
func (h *AIHandler) renderAIInputWithError(w http.ResponseWriter, errorMsg string) {
	data := AIInputData{
		PageData: PageData{
			Title:      "AI Expense Input",
			ActivePage: "ai-input",
		},
		Error: errorMsg,
	}

	if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}

// renderConfirmWithError renders the confirmation page with an error
func (h *AIHandler) renderConfirmWithError(w http.ResponseWriter, r *http.Request, errorMsg string) {
	// Reconstruct the parsed expense from form data
	amountStr := r.FormValue("amount")
	amount, _ := strconv.ParseFloat(amountStr, 64)
	additionalAmountStr := r.FormValue("additional_amount")
	additionalAmount, _ := strconv.ParseFloat(additionalAmountStr, 64)

	parsed := &services.ParsedExpense{
		Amount:           amount,
		Category:         r.FormValue("category"),
		Subcategory:      r.FormValue("subcategory"),
		Description:      r.FormValue("description"),
		Date:             r.FormValue("date"),
		AdditionalAmount: additionalAmount,
	}

	data := AIConfirmData{
		PageData: PageData{
			Title:      "Confirm AI Parsed Expense",
			ActivePage: "ai-input",
		},
		ParsedExpense: parsed,
		Error:         errorMsg,
	}

	if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}
