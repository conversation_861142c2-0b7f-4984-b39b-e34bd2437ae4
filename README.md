# Go Expense Tracker

A lightweight expense tracking application built with Go and SQLite. This application provides a simple REST API for recording and managing personal expenses with Excel export functionality.

## Features

- **Record Expenses**: Add expenses with amount, category, subcategory, description, date, and additional amounts
- **Filter Expenses**: Retrieve expenses with filters by month, year, category, or subcategory
- **Excel Export**: Export filtered expenses to Excel (.xlsx) files
- **SQLite Storage**: File-based database with no external dependencies
- **Standard Library**: Built using only Go's standard library for HTTP handling

## Requirements

- Go 1.21 or later
- No external services required (uses local SQLite database)

## Installation and Setup

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd go-expense
   ```

2. **Install dependencies**:
   ```bash
   go mod tidy
   ```

3. **Run the application**:
   ```bash
   go run main.go
   ```

4. **Access the application**:
   - API Documentation: http://localhost:8080
   - Health Check: http://localhost:8080/health

The application will create a `data/expenses.db` SQLite database file automatically on first run.

## API Endpoints

### POST /expenses
Create a new expense record.

**Request Body**:
```json
{
  "amount": 25.50,
  "category": "Food",
  "subcategory": "Lunch",
  "description": "Pizza lunch",
  "date": "2025-01-15",
  "additional_amount": 3.00
}
```

**Required Fields**:
- `amount` (float): Main expense amount (must be > 0)
- `category` (string): Expense category
- `subcategory` (string): Expense subcategory
- `date` (string): Date in YYYY-MM-DD format

**Optional Fields**:
- `description` (string): Additional description
- `additional_amount` (float): Extra amount (tips, tax, etc.)

**Response**: Returns the created expense with assigned ID and creation timestamp.

### GET /expenses
Retrieve expenses with optional filtering.

**Query Parameters**:
- `month` (1-12): Filter by month
- `year` (1900-2100): Filter by year
- `category`: Filter by category name
- `subcategory`: Filter by subcategory name

**Examples**:
- Get all expenses: `GET /expenses`
- Get January 2025 expenses: `GET /expenses?month=1&year=2025`
- Get food expenses: `GET /expenses?category=Food`
- Get lunch expenses in January: `GET /expenses?month=1&year=2025&subcategory=Lunch`

### GET /expenses/export
Export expenses to Excel file.

**Required Query Parameters**:
- `month` (1-12): Month to export
- `year` (1900-2100): Year to export

**Example**: `GET /expenses/export?month=1&year=2025`

**Response**: Downloads an Excel file named `expenses_YYYY_MM.xlsx` containing:
- All expense details
- Calculated totals
- Summary row with grand totals

## Database Schema

The SQLite database contains an `expenses` table with the following structure:

```sql
CREATE TABLE expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    amount REAL NOT NULL,
    category TEXT NOT NULL,
    subcategory TEXT NOT NULL,
    description TEXT,
    date TEXT NOT NULL,
    additional_amount REAL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Configuration

### Environment Variables
- `PORT`: Server port (default: 8080)

### Database Location
The SQLite database is stored at `./data/expenses.db` relative to the application directory.

## Example Usage

### Adding an Expense
```bash
curl -X POST http://localhost:8080/expenses \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 15.99,
    "category": "Transportation",
    "subcategory": "Gas",
    "description": "Gas station fill-up",
    "date": "2025-01-15",
    "additional_amount": 0
  }'
```

### Getting Expenses
```bash
# Get all expenses
curl http://localhost:8080/expenses

# Get expenses for January 2025
curl "http://localhost:8080/expenses?month=1&year=2025"

# Get food expenses
curl "http://localhost:8080/expenses?category=Food"
```

### Exporting to Excel
```bash
# Download Excel file for January 2025
curl "http://localhost:8080/expenses/export?month=1&year=2025" \
  -o expenses_2025_01.xlsx
```

## Development

### Project Structure
```
go-expense/
├── main.go                 # Application entry point and HTTP server
├── go.mod                  # Go module definition
├── models/
│   └── expense.go         # Expense model and database operations
├── handlers/
│   └── expense_handlers.go # HTTP request handlers
├── database/
│   └── sqlite.go          # Database initialization
├── utils/
│   └── excel.go           # Excel export functionality
├── data/
│   └── expenses.db        # SQLite database (created automatically)
└── README.md              # This file
```

### Dependencies
- `github.com/mattn/go-sqlite3`: SQLite driver
- `github.com/xuri/excelize/v2`: Excel file generation

## Error Handling

The API returns appropriate HTTP status codes:
- `200 OK`: Successful GET requests
- `201 Created`: Successful POST requests
- `400 Bad Request`: Invalid input data
- `405 Method Not Allowed`: Unsupported HTTP methods
- `500 Internal Server Error`: Server-side errors

Error responses include descriptive messages to help with debugging.

## License

This project is open source and available under the MIT License.
