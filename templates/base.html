<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - Go Expense Tracker</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007acc;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: #007acc;
        }

        main {
            min-height: calc(100vh - 200px);
        }

        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #007acc;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #005a9e;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #007acc;
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .filters .form-group {
            margin-bottom: 0;
            min-width: 150px;
        }

        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #fff;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007acc;
        }

        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        /* Form Sections for Better Organization */
        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #007acc;
        }

        .form-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #007acc;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .field-help {
            color: #6c757d;
            font-size: 0.875rem;
            display: block;
            margin-top: 0.25rem;
            line-height: 1.4;
        }

        .optional {
            color: #6c757d;
            font-weight: normal;
            font-size: 0.875rem;
        }

        /* Total Display */
        .total-display {
            background: #e8f4fd;
            border: 1px solid #007acc;
            border-radius: 6px;
            padding: 1rem;
            margin-top: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .total-label {
            font-weight: 600;
            color: #007acc;
        }

        .total-value {
            font-size: 1.25rem;
            font-weight: bold;
            color: #007acc;
        }

        /* Mobile-Optimized Form Actions */
        .form-actions {
            background: #fff;
            padding: 1.5rem;
            margin: 2rem -1.5rem -1.5rem -1.5rem;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 1rem;
            position: sticky;
            bottom: 0;
            z-index: 10;
        }

        .form-actions .btn {
            flex: 1;
            text-align: center;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .btn-primary {
            background: #007acc;
            order: 1;
        }

        .btn-primary:hover {
            background: #005a9e;
        }

        .form-actions .btn-secondary {
            order: 2;
            background: #6c757d;
        }

        @media (max-width: 768px) {
            .nav-links {
                gap: 1rem;
            }

            .filters {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            /* Mobile-specific form optimizations */
            .form-section {
                margin-left: -0.5rem;
                margin-right: -0.5rem;
                padding: 1rem;
            }

            .form-actions {
                margin-left: -1.5rem;
                margin-right: -1.5rem;
                padding: 1rem;
                gap: 0.75rem;
            }

            .form-actions .btn {
                min-height: 52px;
                font-size: 1.1rem;
            }

            /* Larger touch targets for mobile */
            input, select, textarea {
                min-height: 48px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            label {
                font-size: 1rem;
                margin-bottom: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .form-actions {
                flex-direction: column;
            }

            .form-actions .btn {
                width: 100%;
            }

            .btn-primary {
                order: 1;
            }

            .btn-secondary {
                order: 2;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav>
                <a href="/ui" class="logo">💰 Expense Tracker</a>
                <ul class="nav-links">
                    <li><a href="/ui" {{if eq .ActivePage "dashboard"}}class="active"{{end}}>Dashboard</a></li>
                    <li><a href="/ui/expenses" {{if eq .ActivePage "expenses"}}class="active"{{end}}>Expenses</a></li>
                    <li><a href="/ui/add" {{if eq .ActivePage "add"}}class="active"{{end}}>Add Expense</a></li>
                    <li><a href="/ui/summary" {{if eq .ActivePage "summary"}}class="active"{{end}}>Summary</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            {{if eq .ActivePage "dashboard"}}
                {{template "dashboard-content" .}}
            {{else if eq .ActivePage "expenses"}}
                {{template "expenses-content" .}}
            {{else if eq .ActivePage "add"}}
                {{template "add-expense-content" .}}
            {{else if eq .ActivePage "edit"}}
                {{template "add-expense-content" .}}
            {{else if eq .ActivePage "summary"}}
                {{template "summary-content" .}}
            {{else}}
                {{template "dashboard-content" .}}
            {{end}}
        </div>
    </main>

    <script>
        // Simple utility functions for the UI
        function formatCurrency(amount) {
            // Format with dots as thousand separators, no decimal places
            return 'Rp ' + Math.round(amount).toLocaleString('id-ID');
        }

        // Auto-submit forms on filter change
        document.addEventListener('DOMContentLoaded', function() {
            const filterSelects = document.querySelectorAll('.auto-submit');
            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    this.form.submit();
                });
            });
        });
    </script>
</body>
</html>
