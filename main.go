package main

import (
	"fmt"
	"go-expense/database"
	"go-expense/handlers"
	"go-expense/models"
	"go-expense/services"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	// Load environment variables
	openaiAPIKey := os.Getenv("OPENAI_API_KEY")
	if openaiAPIKey == "" {
		log.Println("Warning: OPENAI_API_KEY not set. AI features will not work.")
	}

	dbPath := os.Getenv("DB_PATH")
	if dbPath == "" {
		dbPath = "./data/expenses.db"
	}

	// Initialize database
	db, err := database.InitDB(dbPath)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	// Initialize repository
	repo := models.NewExpenseRepository(db)

	// Create tables
	if err := repo.CreateTable(); err != nil {
		log.Fatal("Failed to create tables:", err)
	}

	// Initialize services
	expenseService := services.NewExpenseService(repo)
	var aiService services.AIServiceInterface
	if openaiAPIKey != "" {
		aiService = services.NewAIService(openaiAPIKey)
	}

	// Initialize handlers
	expenseHandler := handlers.NewExpenseHandler(expenseService)
	uiHandler := handlers.NewUIHandler(expenseService)
	// Always create AI handler, but it will handle missing API key gracefully
	aiHandler := handlers.NewAIHandler(aiService, expenseService)

	// Setup routes
	mux := http.NewServeMux()

	// API routes
	mux.HandleFunc("/expenses", func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodPost:
			expenseHandler.CreateExpense(w, r)
		case http.MethodGet:
			expenseHandler.GetExpenses(w, r)
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	})

	mux.HandleFunc("/expenses/", expenseHandler.DeleteExpense)

	mux.HandleFunc("/expenses/delete/", expenseHandler.DeleteExpense)
	mux.HandleFunc("/expenses/export", expenseHandler.ExportExpenses)
	mux.HandleFunc("/expenses/summary/monthly", expenseHandler.GetMonthlySummary)
	mux.HandleFunc("/expenses/summary/yearly", expenseHandler.GetYearlySummary)

	// UI routes
	mux.HandleFunc("/ui", uiHandler.Dashboard)
	mux.HandleFunc("/ui/", uiHandler.Dashboard)
	mux.HandleFunc("/ui/expenses", uiHandler.Expenses)
	mux.HandleFunc("/ui/add", uiHandler.AddExpense)
	mux.HandleFunc("/ui/edit/", uiHandler.EditExpense)
	mux.HandleFunc("/ui/summary", uiHandler.Summary)

	// AI routes
	mux.HandleFunc("/ui/ai-input", aiHandler.AIInput)
	mux.HandleFunc("/ui/ai-parse", aiHandler.AIParse)
	mux.HandleFunc("/ui/ai-confirm", aiHandler.AIConfirm)

	// Health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, `{"status":"ok","message":"Expense tracker is running"}`)
	})

	// Root endpoint - redirect to UI
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}

		// Redirect to UI dashboard
		http.Redirect(w, r, "/ui", http.StatusTemporaryRedirect)
	})

	// API documentation endpoint
	mux.HandleFunc("/api", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		fmt.Fprint(w, `
<!DOCTYPE html>
<html>
<head>
    <title>Go Expense Tracker API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #333; }
        .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .method { font-weight: bold; color: #007acc; }
        pre { background: #eee; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Go Expense Tracker API</h1>
    <p>A lightweight expense tracking API built with Go and SQLite.</p>
    
    <div class="endpoint">
        <h3><span class="method">POST</span> /expenses</h3>
        <p>Create a new expense</p>
        <pre>{
  "amount": 25.50,
  "category": "Food",
  "subcategory": "Lunch",
  "description": "Pizza lunch",
  "date": "2025-01-15",
  "additional_amount": 3.00
}</pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method">GET</span> /expenses</h3>
        <p>Get all expenses with optional filters</p>
        <p>Query parameters: month, year, category, subcategory</p>
        <p>Example: <code>/expenses?month=1&year=2025&category=Food</code></p>
    </div>
    
    <div class="endpoint">
        <h3><span class="method">GET</span> /expenses/export</h3>
        <p>Export expenses to Excel file</p>
        <p>Required parameters: month, year</p>
        <p>Example: <code>/expenses/export?month=1&year=2025</code></p>
    </div>

    <div class="endpoint">
        <h3><span class="method">GET</span> /expenses/summary/monthly</h3>
        <p>Get monthly expense summary with weekly breakdown</p>
        <p>Required parameters: month, year</p>
        <p>Example: <code>/expenses/summary/monthly?month=1&year=2025</code></p>
    </div>

    <div class="endpoint">
        <h3><span class="method">GET</span> /expenses/summary/yearly</h3>
        <p>Get yearly expense summary with monthly breakdown</p>
        <p>Required parameters: year</p>
        <p>Example: <code>/expenses/summary/yearly?year=2025</code></p>
    </div>

    <div class="endpoint">
        <h3><span class="method">GET</span> /health</h3>
        <p>Health check endpoint</p>
    </div>

    <div style="margin-top: 2rem; padding: 1rem; background: #e7f3ff; border-radius: 5px;">
        <h3>🎉 New Web Interface Available!</h3>
        <p>Try the new web interface at <a href="/ui" style="color: #007acc; font-weight: bold;">/ui</a></p>
        <p>Features include: Dashboard, Expense Management, Filtering, and Summary Reports</p>
    </div>
</body>
</html>`)
	})

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	server := &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}

	// Graceful shutdown
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		log.Println("Shutting down server...")
		if err := server.Close(); err != nil {
			log.Printf("Error during server shutdown: %v", err)
		}
	}()

	log.Printf("Server starting on port %s", port)
	log.Printf("Visit http://localhost:%s for the web interface", port)
	log.Printf("Visit http://localhost:%s/api for API documentation", port)

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatal("Server failed to start:", err)
	}

	log.Println("Server stopped")
}
